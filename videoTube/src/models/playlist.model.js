import mongoose, { Schema } from "mongoose";

const PlaylistSchema = new Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    owner: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true
    },
    videos: [
        {
            type: Schema.Types.ObjectId,
            ref: "Video"
        }
    ],
    isPublic: {
        type: Boolean,
        default: true
    }
}, { timestamps: true });

export const Playlist = mongoose.model("Playlist", PlaylistSchema);