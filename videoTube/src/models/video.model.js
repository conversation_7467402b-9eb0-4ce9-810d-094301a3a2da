import mongoose, { Schema } from "mongoose";
import mongooseAggregatePaginate from "mongoose-paginate-v2";

const VideoSchema = new Schema({
    videoFile:{
        type:String,
        required:true
    },
    thumbnail:{
        type:String,
        required:true,
    },
    title:{
        type:String,
        required:true,
    },
    description:{
        type:String,
        required:true,
    },
    views:{
        type:Number,
        default:0
    },
    duration:{
        type: Number,
        required: true
    },
    isPublished:{
        type:Boolean,
        default:true
    },
    owner:{
        type:Schema.Types.ObjectId,
        required:true,
        ref:"User"
    },
    channel:{
        type:Schema.Types.ObjectId,
        required:true,
        ref:"Channel"
    },
    tags: [{ type: String }],
    category: {
        type: String,
        enum: ['Gaming', 'Music', 'Sports', 'News', 'Entertainment', 'Education', 'Technology', 'Lifestyle', 'Other'],
        default: 'Other'
    },
    visibility: {
        type: String,
        enum: ['public', 'unlisted', 'private'],
        default: 'public'
    },
    likes: {
        type: Number,
        default: 0
    },
    dislikes: {
        type: Number,
        default: 0
    },
},
{timestamps:true}
)

VideoSchema.plugin(mongooseAggregatePaginate)

export const Video = new mongoose.model("Video",VideoSchema)