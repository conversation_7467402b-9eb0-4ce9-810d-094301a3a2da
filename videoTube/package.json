{"name": "videotube", "version": "1.0.0", "description": "A youtube inspired webApp", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/index.js", "start": "nodemon src/index.js"}, "keywords": ["nodemon", "mongoose", "express", "backend"], "author": "<PERSON><PERSON>", "license": "ISC", "type": "module", "devDependencies": {"nodemon": "^3.1.10", "prettier": "^3.6.2"}, "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "mongoose-paginate-v2": "^1.9.1", "multer": "^2.0.1"}}